# py_mydataretriever/configs/settings.py

"""
Module: settings
================

Contains configuration variables and constants used throughout the project.

"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
ENV_PATH = os.path.join(os.path.dirname(__file__), '..', '..', '.env')
load_dotenv(dotenv_path=ENV_PATH)

# Google API Configuration
CLIENT_SECRET_FILE = os.getenv(
    "GOOGLE_CLIENT_SECRET_FILE",
    os.path.join(os.path.dirname(__file__), 'credentials.json')
)
TOKEN_FILE = os.getenv(
    "GOOGLE_TOKEN_FILE",
    os.path.join(os.path.dirname(__file__), 'token.json')
)
SCOPES = os.getenv(
    "GOOGLE_SCOPES",
    "https://www.googleapis.com/auth/gmail.readonly"
).split()

# Data Directories
DATA_DIR_RAW = os.path.join(os.path.dirname(__file__), '..', 'data', 'raw', 'emails')
DATA_DIR_PROCESSED = os.path.join(os.path.dirname(__file__), '..', 'data', 'processed', 'emails')

# Ensure data directories exist
os.makedirs(DATA_DIR_RAW, exist_ok=True)
os.makedirs(DATA_DIR_PROCESSED, exist_ok=True)
