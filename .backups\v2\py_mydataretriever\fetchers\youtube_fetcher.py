# py_mydataretriever/fetchers/youtube_fetcher.py

"""
Module: youtube_fetcher
=====================

YouTube Data API integration for playlist data retrieval.

"""

# fetchers/youtube_fetcher.py

"""YouTube Data API integration for playlist data retrieval"""

import os
from typing import List, Dict, Optional
from datetime import datetime

from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from tqdm import tqdm

from py_mydataretriever.utils.logging_config import logger
from py_mydataretriever.utils.file_utils import (
    generate_filename,
    append_to_json,
    load_processed_items,
    save_processed_item
)
from py_mydataretriever.configs.settings import (
    CLIENT_SECRET_FILE,
    TOKEN_FILE,
    DATA_DIR_RAW,
    DATA_DIR_PROCESSED
)

# YouTube API scope for read-only access to playlists
YOUTUBE_SCOPE = ["https://www.googleapis.com/auth/youtube.readonly"]

def authenticate() -> Credentials:
    """Handle YouTube Data API authentication flow with token caching"""
    creds = None
    if os.path.exists(TOKEN_FILE):
        creds = Credentials.from_authorized_user_file(TOKEN_FILE, YOUTUBE_SCOPE)

    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_FILE, YOUTUBE_SCOPE)
            creds = flow.run_local_server(port=0)
        with open(TOKEN_FILE, "w") as token:
            token.write(creds.to_json())

    return creds

def get_playlist_items(service, playlist_id: str, page_token: Optional[str] = None) -> Dict:
    """Fetch items from specified playlist with pagination support"""
    return service.playlistItems().list(
        part="snippet,contentDetails",
        playlistId=playlist_id,
        maxResults=50,
        pageToken=page_token
    ).execute()

def extract_video_data(item: Dict) -> Dict:
    """Extract relevant video information from playlist item"""
    snippet = item["snippet"]
    content = item["contentDetails"]

    return {
        "video_id": content["videoId"],
        "title": snippet["title"],
        "description": snippet["description"],
        "published_at": snippet["publishedAt"],
        "channel_id": snippet["videoOwnerChannelId"],
        "channel_title": snippet["videoOwnerChannelTitle"],
        "position": snippet["position"]
    }

def fetch_playlist(
    playlist_id: str,
    max_results: int = 100,
    include_unlisted: bool = False
) -> None:
    """
    Fetch and store video data from specified YouTube playlist

    Args:
        playlist_id: YouTube playlist identifier
        max_results: Maximum number of videos to fetch
        include_unlisted: Whether to include unlisted videos
    """
    creds = authenticate()
    service = build("youtube", "v3", credentials=creds)

    # Setup file paths
    base_name = f"youtube_playlist_{playlist_id}"
    json_file = os.path.join(DATA_DIR_RAW, "youtube", f"{base_name}.json")
    processed_file = os.path.join(DATA_DIR_PROCESSED, "youtube", f"{base_name}_processed.txt")

    # Ensure directories exist
    os.makedirs(os.path.dirname(json_file), exist_ok=True)
    os.makedirs(os.path.dirname(processed_file), exist_ok=True)

    processed_items = load_processed_items(processed_file)
    total_fetched = 0
    next_page_token = None

    with tqdm(total=max_results, desc="Fetching Playlist Items", unit="video") as pbar:
        while total_fetched < max_results:
            try:
                response = get_playlist_items(service, playlist_id, next_page_token)
                items = response.get("items", [])

                if not items:
                    break

                # Process and store new items
                new_items = []
                for item in items:
                    video_id = item["contentDetails"]["videoId"]
                    if video_id not in processed_items:
                        video_data = extract_video_data(item)
                        new_items.append(video_data)
                        save_processed_item(video_id, processed_file)

                if new_items:
                    append_to_json(new_items, json_file)
                    fetched = len(new_items)
                    total_fetched += fetched
                    pbar.update(fetched)
                    logger.info(f"Fetched {fetched} new videos. Total: {total_fetched}")

                next_page_token = response.get("nextPageToken")
                if not next_page_token:
                    break

            except Exception as e:
                logger.error(f"Error fetching playlist items: {str(e)}")
                break

    logger.success(f"Finished fetching {total_fetched} videos from playlist {playlist_id}")

def list_my_playlists() -> List[Dict]:
    """Fetch list of authenticated user's playlists"""
    creds = authenticate()
    service = build("youtube", "v3", credentials=creds)

    playlists = []
    next_page_token = None

    while True:
        request = service.playlists().list(
            part="snippet",
            mine=True,
            maxResults=50,
            pageToken=next_page_token
        )
        response = request.execute()

        for item in response["items"]:
            playlists.append({
                "id": item["id"],
                "title": item["snippet"]["title"],
                "description": item["snippet"]["description"],
                "video_count": item["contentDetails"]["itemCount"]
            })

        next_page_token = response.get("nextPageToken")
        if not next_page_token:
            break

    return playlists
