# scripts/fetch_youtube_playlists.py

"""
Script: fetch_youtube_playlists
====================

Script for fetching YouTube playlist data.

"""

import sys
import os
import argparse

sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from py_mydataretriever.fetchers.youtube_fetcher import fetch_playlist, list_my_playlists

def main():
    parser = argparse.ArgumentParser(description="Fetch data from YouTube playlists")
    parser.add_argument("--list", action="store_true", help="List your playlists")
    parser.add_argument("--playlist-id", type=str, help="YouTube playlist ID to fetch")
    parser.add_argument("--max-results", type=int, default=100, help="Maximum number of videos to fetch")
    parser.add_argument("--include-unlisted", action="store_true", help="Include unlisted videos")

    args = parser.parse_args()

    if args.list:
        playlists = list_my_playlists()
        print("\nYour Playlists:")
        for playlist in playlists:
            print(f"\nID: {playlist['id']}")
            print(f"Title: {playlist['title']}")
            print(f"Videos: {playlist['video_count']}")
    elif args.playlist_id:
        fetch_playlist(
            playlist_id=args.playlist_id,
            max_results=args.max_results,
            include_unlisted=args.include_unlisted
        )
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
