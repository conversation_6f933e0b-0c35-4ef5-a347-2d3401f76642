# scripts/fetch_emails.py

"""
Script: fetch_emails
====================

Initiates the email fetching process using Gmail Fetcher.

"""

import sys
import os

# Adjust the system path to import the package
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from py_mydataretriever.fetchers.gmail_fetcher import fetch_emails

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Fetch emails from Gmail.")
    parser.add_argument("--sender", type=str, help="Specify the sender email address")
    parser.add_argument("--recipient", type=str, help="Specify the recipient email address")
    parser.add_argument(
        "--max_results", type=int, default=10, help="Maximum number of emails to fetch"
    )
    args = parser.parse_args()
    fetch_emails(sender=args.sender, recipient=args.recipient, max_results=args.max_results)
