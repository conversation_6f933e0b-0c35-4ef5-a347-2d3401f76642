# Function: Gmail Email Fetcher
# =======================================================
# Fetch emails from a Gmail account using the Gmail API. The script allows filtering
# by sender or recipient, supports saving results to CSV and JSON files, and tracks
# processed emails to avoid duplicates.
# =======================================================

# Imports
# -------------------------------------------------------
from tqdm import tqdm
from loguru import logger
import os
import re
import csv
import json
import argparse
from dotenv import load_dotenv
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# Load Environment Variables
# -------------------------------------------------------
load_dotenv()
CLIENT_SECRET_FILE = os.getenv("GOOGLE_CLIENT_SECRET_FILE", "credentials.json")
TOKEN_FILE = os.getenv("GOOGLE_TOKEN_FILE", "token.json")
SCOPES = os.getenv("GOOGLE_SCOPES", "https://www.googleapis.com/auth/gmail.readonly").split()

# Configure loguru
# -------------------------------------------------------
logger.remove()
logger.add("email_fetcher.log", rotation="10 MB", retention="10 days", level="INFO")

# Authentication
# =======================================================
def authenticate():
    """ Authenticate and retrieve credentials for Gmail API. """
    creds = None
    if os.path.exists(TOKEN_FILE):
        creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
            logger.info("Credentials refreshed.")
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_FILE, SCOPES)
            creds = flow.run_local_server(port=0)
            logger.info("New credentials obtained.")
        with open(TOKEN_FILE, "w") as token:
            token.write(creds.to_json())
            logger.info("Credentials saved to token file.")
    return creds


# Function: Generate Filename
# =======================================================
def generate_filename(base_name, sender=None, recipient=None, suffix="", file_type="csv"):
    """ Generate a consistent filename based on sender, recipient, and suffix. """
    parts = [base_name]
    if sender:
        parts.append(f"from_{sender.replace('@', '_').replace('.', '_')}")
    if recipient:
        parts.append(f"to_{recipient.replace('@', '_').replace('.', '_')}")
    if suffix:
        parts.append(suffix)
    return f"{'_'.join(parts)}.{file_type}"


# Function: Append Emails to CSV
# =======================================================
def append_emails_to_csv(emails, file_path):
    """ Append a list of emails to a CSV file. """
    with open(file_path, mode="a", newline="", encoding="utf-8-sig") as file:
        writer = csv.writer(file)
        for email in emails:
            writer.writerow(
                [
                    email["date"],
                    email["from"],
                    email["to"],
                    email["subject"],
                    email["snippet"],
                ]
            )


# Function: Append Emails to JSON
# =======================================================
def append_emails_to_json(emails, file_path):
    """ Append a list of emails to a JSON file. """
    if not os.path.exists(file_path):
        with open(file_path, "w", encoding="utf-8") as file:
            json.dump([], file)
    with open(file_path, "r+", encoding="utf-8") as file:
        existing_data = json.load(file)
        existing_data.extend(emails)
        file.seek(0)
        json.dump(existing_data, file, ensure_ascii=False, indent=4)


# Function: Load Processed Emails
# =======================================================
def load_processed_emails(file_path):
    """ Load the list of already processed email IDs. """
    if os.path.exists(file_path):
        with open(file_path, "r") as f:
            return set(f.read().splitlines())
    return set()


# Function: Save Processed Email ID
# =======================================================
def save_processed_email(email_id, file_path):
    """ Append an email ID to the processed emails file. """
    with open(file_path, "a") as f:
        f.write(email_id + "\n")


# Function: Safe Strip
# =======================================================
def safe_strip(value):
    """ Safely strip a value; returns an empty string if None. """
    return value.strip() if value else ""


# Function: Get Header Value
# =======================================================
def get_header_value(headers, name):
    """ Retrieve the value of a specific header by its name. """
    for header in headers:
        if header["name"].lower() == name.lower():
            return header["value"]
    return None


# Function: Clean Sender
# =======================================================
def clean_sender(sender):
    """ Standardize the format of the sender field. """
    match = re.match(r'(?:"?([^"]*)"?\s)?(?:<?(.+@[^>]+)>?)', sender)
    if match:
        name, email = match.groups()
        return f"{name.strip()} <{email.strip()}>" if name else f"{email.strip()}"
    return sender


# Function: Fetch Emails
# =======================================================
def fetch_emails(sender=None, recipient=None, max_results=10):
    """ Fetch emails from Gmail based on sender and recipient filters. """
    creds = authenticate()
    service = build("gmail", "v1", credentials=creds)

    # Prepare file paths
    base_name = "emails"
    csv_file_path = generate_filename(base_name, sender, recipient, file_type="csv")
    json_file_path = generate_filename(base_name, sender, recipient, file_type="json")
    processed_emails_file = generate_filename(base_name, sender, recipient, suffix="processed", file_type="txt")
    processed_emails = load_processed_emails(processed_emails_file)

    # Build query string and fetch emails
    query_string = build_query(sender, recipient)
    total_fetched = 0
    next_page_token = None


    with tqdm(total=max_results, desc="Fetching Emails", unit="email") as pbar:
        while total_fetched < max_results:
            messages, next_page_token = fetch_message_ids(
                service, query_string, max_results - total_fetched, next_page_token
            )
            if not messages:
                logger.info(f"No more emails found matching query: {query_string}")
                break
            emails = process_messages(service, messages, processed_emails, processed_emails_file)
            save_emails(emails, csv_file_path, json_file_path)
            fetched = len(emails)
            total_fetched += fetched
            logger.info(f"Fetched {fetched} emails this batch. Total fetched: {total_fetched}")
            pbar.update(fetched)
            if not next_page_token:
                break
    logger.success(f"Finished fetching {total_fetched} emails.")


# Function: Build Query
# =======================================================
def build_query(sender, recipient):
    """ Construct a Gmail search query string. """
    query = []
    if sender:
        query.append(f"from:{sender}")
    if recipient:
        query.append(f"to:{recipient}")
    return " ".join(query)


# Function: Fetch Message IDs
# =======================================================
def fetch_message_ids(service, query_string, max_results, next_page_token):
    """ Fetch message IDs matching the query string. """
    result = (
        service.users()
        .messages()
        .list(
            userId="me",
            q=query_string,
            maxResults=min(100, max_results),
            pageToken=next_page_token,
        )
        .execute()
    )
    return result.get("messages", []), result.get("nextPageToken")


# Function: Process Messages
# =======================================================
def process_messages(service, messages, processed_emails, processed_emails_file):
    """ Retrieve and process messages by fetching details like subject and sender. """
    emails = []
    for message in messages:
        email_id = message["id"]
        if email_id in processed_emails:
            continue
        msg = service.users().messages().get(userId="me", id=email_id).execute()
        headers = msg["payload"]["headers"]
        email_data = {
            "date": safe_strip(get_header_value(headers, "Date")),
            "from": safe_strip(clean_sender(get_header_value(headers, "From"))),
            "to": safe_strip(clean_sender(get_header_value(headers, "To")))
            if get_header_value(headers, "To")
            else "",
            "subject": safe_strip(get_header_value(headers, "Subject")),
            "snippet": safe_strip(msg.get("snippet", "")),
        }
        emails.append(email_data)
        save_processed_email(email_id, processed_emails_file)
    return emails


# Function: Save Emails
# =======================================================
def save_emails(emails, csv_file_path, json_file_path):
    """Save emails to CSV and JSON files."""
    if emails:
        append_emails_to_csv(emails, csv_file_path)
        append_emails_to_json(emails, json_file_path)


# Main Execution
# =======================================================
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fetch emails from Gmail.")
    parser.add_argument("--sender", type=str, help="Specify the sender email address")
    parser.add_argument("--recipient", type=str, help="Specify the recipient email address")
    parser.add_argument("--max_results", type=int, default=10, help="Specify the maximum number of emails to fetch")
    args = parser.parse_args()
    fetch_emails(sender=args.sender, recipient=args.recipient, max_results=args.max_results)
