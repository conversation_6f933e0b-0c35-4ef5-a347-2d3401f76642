# setup.py

from setuptools import setup, find_packages

setup(
    name='py_mydataretriever',
    version='0.1.0',
    author='Jorn',
    description='something',
    # long_description=open('README.md').read(),
    # long_description_content_type='text/markdown',
    packages=find_packages(),
    # install_requires=[
    #     'requests',
    #     'loguru',
    #     'rich'
    # ],
    # classifiers=[
    #     'Programming Language :: Python :: 3',
    #     'License :: OSI Approved :: MIT License',
    #     'Operating System :: OS Independent',
    # ],
    # python_requires='>=3.8',
    # entry_points={
    #     'console_scripts': [
    #         'py_my_git_toolkit=git_kit.main:main',
    #     ],
    # },
)
