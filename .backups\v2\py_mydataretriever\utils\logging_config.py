# py_mydataretriever/utils/logging_config.py

"""
Module: logging_config
======================

Configures logging behavior for the project using loguru.

"""

from loguru import logger
import sys
import os

# Configure loguru logger
logger.remove()  # Remove default handler
logger.add(
    sys.stderr,
    level="INFO",
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
           "<level>{level: <8}</level> | "
           "<cyan>{module}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
           "<level>{message}</level>",
)

# Optional: Add file handler
LOG_DIR = os.path.join(os.path.dirname(__file__), '..', 'logs')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(
    os.path.join(LOG_DIR, "app.log"),
    rotation="10 MB",
    retention="10 days",
    level="INFO",
    compression="zip",
    serialize=False,
    backtrace=True,
    diagnose=True,
)
