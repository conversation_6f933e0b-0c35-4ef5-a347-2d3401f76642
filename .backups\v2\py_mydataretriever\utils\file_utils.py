# py_mydataretriever/utils/file_utils.py

"""
Module: file_utils
==================

Utility functions for file operations such as generating filenames,
appending data to files, and loading processed items.

"""

import os
import csv
import json
from typing import List, Dict, Set, Optional

def generate_filename(
    base_name: str,
    sender: Optional[str] = None,
    recipient: Optional[str] = None,
    suffix: str = "",
    file_type: str = "csv",
) -> str:
    """
    Generate a consistent filename based on sender, recipient, and suffix.

    Args:
        base_name (str): Base name for the file.
        sender (Optional[str]): Sender email address.
        recipient (Optional[str]): Recipient email address.
        suffix (str): Additional suffix for the filename.
        file_type (str): File extension/type.

    Returns:
        str: Generated filename.
    """
    parts = [base_name]
    if sender:
        parts.append(f"from_{sanitize_email(sender)}")
    if recipient:
        parts.append(f"to_{sanitize_email(recipient)}")
    if suffix:
        parts.append(suffix)
    filename = f"{'_'.join(parts)}.{file_type}"
    return filename

def sanitize_email(email: str) -> str:
    """
    Sanitize an email address for use in filenames.

    Args:
        email (str): Email address to sanitize.

    Returns:
        str: Sanitized email address.
    """
    return email.replace("@", "_at_").replace(".", "_dot_")

def append_emails_to_csv(emails: List[Dict], file_path: str):
    """
    Append a list of emails to a CSV file.

    Args:
        emails (List[Dict]): List of email data dictionaries.
        file_path (str): File path to the CSV file.
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    file_exists = os.path.isfile(file_path)
    with open(file_path, mode="a", newline="", encoding="utf-8-sig") as file:
        writer = csv.writer(file)
        if not file_exists:
            # Write header if file doesn't exist
            writer.writerow(["date", "from", "to", "subject", "snippet"])
        for email in emails:
            writer.writerow(
                [
                    email["date"],
                    email["from"],
                    email["to"],
                    email["subject"],
                    email["snippet"],
                ]
            )

def append_emails_to_json(emails: List[Dict], file_path: str):
    """
    Append a list of emails to a JSON file.

    Args:
        emails (List[Dict]): List of email data dictionaries.
        file_path (str): File path to the JSON file.
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    if not os.path.exists(file_path):
        with open(file_path, "w", encoding="utf-8") as file:
            json.dump([], file)
    with open(file_path, "r+", encoding="utf-8") as file:
        existing_data = json.load(file)
        existing_data.extend(emails)
        file.seek(0)
        json.dump(existing_data, file, ensure_ascii=False, indent=4)

def load_processed_emails(file_path: str) -> Set[str]:
    """
    Load the list of already processed email IDs.

    Args:
        file_path (str): File path to the processed emails file.

    Returns:
        Set[str]: Set of processed email IDs.
    """
    if os.path.exists(file_path):
        with open(file_path, "r") as f:
            return set(f.read().splitlines())
    return set()

def save_processed_email(email_id: str, file_path: str):
    """
    Append an email ID to the processed emails file.

    Args:
        email_id (str): Email ID to save.
        file_path (str): File path to the processed emails file.
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "a") as f:
        f.write(email_id + "\n")

def safe_strip(value: Optional[str]) -> str:
    """
    Safely strip a value; returns an empty string if None.

    Args:
        value (Optional[str]): String value to strip.

    Returns:
        str: Stripped string or empty string if None.
    """
    return value.strip() if value else ""
