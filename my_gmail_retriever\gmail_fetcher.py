# Gmail Email Fetcher
# =======================================================
# Fetch emails from a Gmail account using the Gmail API.
# Supports filtering by sender or recipient, saving results JSON files,
# and tracking processed emails to avoid duplicates.
# =======================================================

# Imports
# -------------------------------------------------------
from tqdm import tqdm
from loguru import logger
from pathlib import Path
import re
import json
import argparse
import os
from dotenv import load_dotenv
from email.utils import parseaddr
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# Define Directories
# -------------------------------------------------------
SCRIPT_DIR = Path(__file__).parent.resolve()
DATA_DIR = SCRIPT_DIR / "data"
LOGS_DIR = SCRIPT_DIR / "logs"
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Load Environment Variables
# -------------------------------------------------------
load_dotenv(SCRIPT_DIR / ".env")
CLIENT_SECRET_FILE = SCRIPT_DIR / Path(os.getenv("GOOGLE_CLIENT_SECRET_FILE", "credentials.json"))
TOKEN_FILE = SCRIPT_DIR / Path(os.getenv("GOOGLE_TOKEN_FILE", "token.json"))
SCOPES = os.getenv("GOOGLE_SCOPES", "https://www.googleapis.com/auth/gmail.readonly").split()

# Configure loguru for structured logging
# -------------------------------------------------------
logger.remove()
log_file = LOGS_DIR / "email_fetcher.log"
logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)

# Utility Functions
# -------------------------------------------------------
def sanitize_filename_component(component):
    """Replace invalid filename characters with underscores."""
    return re.sub(r'[^A-Za-z0-9_-]', '_', component)

def clean_sender(sender):
    """Standardize the sender format using email.utils.parseaddr."""
    name, email_addr = parseaddr(sender)
    return f"{name.strip()} <{email_addr.strip()}>" if name else email_addr.strip()

def sanitize_filename(base_name, sender=None, recipient=None, suffix="", file_type="json"):
    """Generate a sanitized filename based on provided components."""
    parts = [base_name]
    if sender:
        sender_sanitized = sanitize_filename_component(sender.replace('@', '_').replace('.', '_'))
        parts.append(f"from_{sender_sanitized}")
    if recipient:
        recipient_sanitized = sanitize_filename_component(recipient.replace('@', '_').replace('.', '_'))
        parts.append(f"to_{recipient_sanitized}")
    if suffix:
        suffix_sanitized = sanitize_filename_component(suffix)
        parts.append(suffix_sanitized)
    filename = f"{'_'.join(parts)}.{file_type}"
    return DATA_DIR / filename

# GmailFetcher Class
# -------------------------------------------------------
class GmailFetcher:
    """Fetch and process emails from Gmail."""

    def __init__(self, sender=None, recipient=None, max_results=10):
        self.sender = sender
        self.recipient = recipient
        self.max_results = max_results
        self.creds = self.authenticate()
        self.service = build("gmail", "v1", credentials=self.creds)
        self.query_string = self.build_query()
        self.processed_emails_file = sanitize_filename("emails", sender, recipient, suffix="processed", file_type="txt")
        self.processed_emails = self.load_processed_emails()

    def authenticate(self):
        """Authenticate and obtain Gmail API credentials."""
        creds = None
        if TOKEN_FILE.exists():
            creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    logger.info("Credentials refreshed.")
                except Exception as e:
                    logger.warning(f"Failed to refresh credentials: {e}")
                    logger.info("Removing invalid token file and re-authenticating...")
                    if TOKEN_FILE.exists():
                        TOKEN_FILE.unlink()
                    creds = None

            if not creds:
                flow = InstalledAppFlow.from_client_secrets_file(str(CLIENT_SECRET_FILE), SCOPES)
                creds = flow.run_local_server(port=0)
                logger.info("New credentials obtained.")

            TOKEN_FILE.write_text(creds.to_json())
            logger.info("Credentials saved to token file.")
        return creds

    def build_query(self):
        """Construct Gmail API query string based on sender and recipient."""
        query = []
        if self.sender:
            query.append(f"from:{self.sender}")
        if self.recipient:
            query.append(f"to:{self.recipient}")
        return " ".join(query)

    def load_processed_emails(self):
        """Load processed email IDs to avoid duplication."""
        if self.processed_emails_file.exists():
            return set(self.processed_emails_file.read_text().splitlines())
        return set()

    def save_processed_email(self, email_id):
        """Mark an email ID as processed."""
        self.processed_emails_file.parent.mkdir(parents=True, exist_ok=True)
        with self.processed_emails_file.open("a") as f:
            f.write(email_id + "\n")
        self.processed_emails.add(email_id)

    def fetch_message_ids(self, max_results, next_page_token=None):
        """Retrieve message IDs from Gmail API."""
        result = self.service.users().messages().list(
            userId="me",
            q=self.query_string,
            maxResults=min(100, max_results),
            pageToken=next_page_token,
        ).execute()
        return result.get("messages", []), result.get("nextPageToken")

    def process_messages(self, messages):
        """Fetch and structure email data from message IDs."""
        emails = []
        for message in messages:
            email_id = message["id"]
            if email_id in self.processed_emails:
                continue
            msg = self.service.users().messages().get(userId="me", id=email_id).execute()
            headers = msg["payload"]["headers"]
            email_data = {
                "id": email_id,
                "date": self.safe_strip(self.get_header_value(headers, "Date")),
                "from": self.safe_strip(clean_sender(self.get_header_value(headers, "From"))),
                "to": self.safe_strip(clean_sender(self.get_header_value(headers, "To"))) if self.get_header_value(headers, "To") else "",
                "subject": self.safe_strip(self.get_header_value(headers, "Subject")),
                "snippet": self.safe_strip(msg.get("snippet", "")),
            }
            emails.append(email_data)
            self.save_processed_email(email_id)
        return emails

    def save_emails(self, emails):
        """Save emails to JSON."""
        if not emails:
            return

        json_file_path = sanitize_filename("emails", self.sender, self.recipient, file_type="json")
        self.append_emails_to_json(emails, json_file_path)

    def append_emails_to_json(self, emails, file_path):
        """Append email data to a JSON file as a single JSON array."""
        existing_emails = []
        if file_path.exists():
            try:
                existing_emails = json.load(file_path.open("r", encoding="utf-8"))
                if not isinstance(existing_emails, list):
                    existing_emails = []
            except json.JSONDecodeError:
                existing_emails = []
        existing_emails.extend(emails)
        with file_path.open("w", encoding="utf-8") as file:
            json.dump(existing_emails, file, ensure_ascii=False, indent=4)

    def safe_strip(self, value):
        """Strip whitespace from a string, return empty string if None."""
        return value.strip() if value else ""

    def get_header_value(self, headers, name):
        """Extract the value of a specific email header."""
        return next((header["value"] for header in headers if header["name"].lower() == name.lower()), None)

    def fetch_emails(self):
        """Main method to fetch and save emails."""
        total_fetched = 0
        next_page_token = None

        with tqdm(total=self.max_results, desc="Fetching Emails", unit="email") as pbar:
            while total_fetched < self.max_results:
                messages, next_page_token = self.fetch_message_ids(
                    max_results=self.max_results - total_fetched,
                    next_page_token=next_page_token
                )
                if not messages:
                    logger.info(f"No more emails found matching query: {self.query_string}")
                    break
                emails = self.process_messages(messages)
                self.save_emails(emails)
                fetched = len(emails)
                total_fetched += fetched
                logger.info(f"Fetched {fetched} emails this batch. Total fetched: {total_fetched}")
                pbar.update(fetched)
                if not next_page_token:
                    break
        logger.success(f"Finished fetching {total_fetched} emails.")

# Command-Line Interface Enhancements
# -------------------------------------------------------
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Fetch emails from Gmail using the Gmail API.",
        epilog="Example: python email_fetcher.py --sender <EMAIL> --recipient <EMAIL> --max_results 50"
    )
    parser.add_argument(
        "--sender",
        type=str,
        help="Specify the sender email address to filter."
    )
    parser.add_argument(
        "--recipient",
        type=str,
        help="Specify the recipient email address to filter."
    )
    parser.add_argument(
        "--max_results",
        type=int,
        default=10,
        help="Specify the maximum number of emails to fetch (default: 10)."
    )
    parser.add_argument(
        "--data_dir",
        type=str,
        default="data",
        help="Specify the directory to save data files (default: data)."
    )
    parser.add_argument(
        "--logs_dir",
        type=str,
        default="logs",
        help="Specify the directory to save log files (default: logs)."
    )
    return parser.parse_args()

# Main Execution Flow
# -------------------------------------------------------
def main():
    """Execute the email fetching process based on CLI arguments."""
    args = parse_arguments()

    # Update directories based on arguments
    data_dir = Path(args.data_dir)
    logs_dir = Path(args.logs_dir)
    data_dir.mkdir(parents=True, exist_ok=True)
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Reconfigure logger to use the specified logs directory
    global logger
    logger.remove()
    log_file = logs_dir / "email_fetcher.log"
    logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)

    fetcher = GmailFetcher(
        sender=args.sender,
        recipient=args.recipient,
        max_results=args.max_results,
    )
    fetcher.fetch_emails()

if __name__ == "__main__":
    main()
