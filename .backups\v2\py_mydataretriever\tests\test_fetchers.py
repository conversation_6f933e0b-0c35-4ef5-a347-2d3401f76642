# py_mydataretriever/tests/test_fetchers.py

"""
Module: test_fetchers
=====================

Unit tests for the fetchers package.

"""

import unittest
from py_mydataretriever.fetchers.gmail_fetcher import build_query

class TestGmailFetcher(unittest.TestCase):
    def test_build_query(self):
        self.assertEqual(build_query("<EMAIL>", None), "from:<EMAIL>")
        self.assertEqual(build_query(None, "<EMAIL>"), "to:<EMAIL>")
        self.assertEqual(
            build_query("<EMAIL>", "<EMAIL>"),
            "from:<EMAIL> to:<EMAIL>"
        )

if __name__ == "__main__":
    unittest.main()
