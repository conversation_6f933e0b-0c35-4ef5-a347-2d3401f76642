# py_mydataretriever/tests/test_utils.py

"""
Module: test_utils
==================

Unit tests for the utils package.

"""

import unittest
from py_mydataretriever.utils.file_utils import sanitize_email

class TestFileUtils(unittest.TestCase):
    def test_sanitize_email(self):
        email = "<EMAIL>"
        expected = "user_at_example_dot_com"
        self.assertEqual(sanitize_email(email), expected)

if __name__ == "__main__":
    unittest.main()
