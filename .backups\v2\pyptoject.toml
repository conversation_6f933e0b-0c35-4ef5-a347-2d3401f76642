[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "py_mydataretriever"
version = "0.1.0"
authors = [{ name = "<PERSON><PERSON>" }]
description = "A utility for automating interactions with Git repositories."
readme = "README.md"
license = { file = "LICENSE" }
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "requests",
    "loguru",
    "rich"
]
requires-python = ">=3.8"

[project.scripts]
py_my_git_toolkit = "py_package_name.main:main"
