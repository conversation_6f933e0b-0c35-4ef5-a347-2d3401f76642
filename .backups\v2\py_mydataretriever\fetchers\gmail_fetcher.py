# py_mydataretriever/fetchers/gmail_fetcher.py

"""
Module: gmail_fetcher
=====================

Fetch emails from a Gmail account using the Gmail API. Supports filtering by
sender or recipient, saving results to CSV and JSON files, and tracking
processed emails to avoid duplicates.

"""

import os
import re
from typing import List, Dict, Optional, Set

from tqdm import tqdm
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

from py_mydataretriever.utils.logging_config import logger
from py_mydataretriever.utils.file_utils import (
    generate_filename,
    append_emails_to_csv,
    append_emails_to_json,
    load_processed_emails,
    save_processed_email,
    safe_strip,
)
from py_mydataretriever.configs.settings import (
    CLIENT_SECRET_FILE,
    TOKEN_FILE,
    SCOPES,
    DATA_DIR_RAW,
    DATA_DIR_PROCESSED,
)


def authenticate() -> Credentials:
    """
    Authenticate and retrieve credentials for Gmail API.

    Returns:
        Credentials: Authenticated credentials object.
    """
    creds: Optional[Credentials] = None
    if os.path.exists(TOKEN_FILE):
        creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
        logger.info("Loaded existing credentials from token file.")
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
            logger.info("Credentials refreshed.")
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_FILE, SCOPES)
            creds = flow.run_local_server(port=0)
            logger.info("New credentials obtained.")
        with open(TOKEN_FILE, "w") as token:
            token.write(creds.to_json())
            logger.info("Credentials saved to token file.")
    return creds


def build_query(sender: Optional[str], recipient: Optional[str]) -> str:
    """
    Construct a Gmail search query string.

    Args:
        sender (Optional[str]): Sender email address.
        recipient (Optional[str]): Recipient email address.

    Returns:
        str: Gmail API query string.
    """
    query = []
    if sender:
        query.append(f"from:{sender}")
    if recipient:
        query.append(f"to:{recipient}")
    return " ".join(query)


def fetch_message_ids(
    service, query_string: str, max_results: int, next_page_token: Optional[str]
) -> (List[Dict], Optional[str]):
    """
    Fetch message IDs matching the query string.

    Args:
        service: Gmail API service instance.
        query_string (str): Gmail API query string.
        max_results (int): Maximum number of results to fetch.
        next_page_token (Optional[str]): Token for pagination.

    Returns:
        Tuple[List[Dict], Optional[str]]: List of message IDs and next page token.
    """
    result = (
        service.users()
        .messages()
        .list(
            userId="me",
            q=query_string,
            maxResults=min(100, max_results),
            pageToken=next_page_token,
        )
        .execute()
    )
    return result.get("messages", []), result.get("nextPageToken")


def get_header_value(headers: List[Dict], name: str) -> Optional[str]:
    """
    Retrieve the value of a specific header by its name.

    Args:
        headers (List[Dict]): List of email headers.
        name (str): Header name to search for.

    Returns:
        Optional[str]: Header value if found, else None.
    """
    for header in headers:
        if header["name"].lower() == name.lower():
            return header["value"]
    return None


def clean_email_address(email_address: str) -> str:
    """
    Standardize the format of an email address.

    Args:
        email_address (str): Raw email address string.

    Returns:
        str: Cleaned email address.
    """
    match = re.match(r'(?:"?([^"]*)"?\s)?(?:<?(.+@[^>]+)>?)', email_address)
    if match:
        name, email = match.groups()
        if name:
            return f"{name.strip()} <{email.strip()}>"
        else:
            return email.strip()
    return email_address.strip()


def process_messages(
    service,
    messages: List[Dict],
    processed_emails: Set[str],
    processed_emails_file: str,
) -> List[Dict]:
    """
    Retrieve and process messages by fetching details like subject and sender.

    Args:
        service: Gmail API service instance.
        messages (List[Dict]): List of message metadata.
        processed_emails (Set[str]): Set of already processed email IDs.
        processed_emails_file (str): File path to save processed email IDs.

    Returns:
        List[Dict]: List of processed email data.
    """
    emails = []
    for message in messages:
        email_id = message["id"]
        if email_id in processed_emails:
            continue
        msg = service.users().messages().get(userId="me", id=email_id).execute()
        headers = msg["payload"]["headers"]
        email_data = {
            "date": safe_strip(get_header_value(headers, "Date")),
            "from": safe_strip(clean_email_address(get_header_value(headers, "From"))),
            "to": safe_strip(
                clean_email_address(get_header_value(headers, "To"))
                if get_header_value(headers, "To")
                else ""
            ),
            "subject": safe_strip(get_header_value(headers, "Subject")),
            "snippet": safe_strip(msg.get("snippet", "")),
        }
        emails.append(email_data)
        save_processed_email(email_id, processed_emails_file)
    return emails


def save_emails(emails: List[Dict], csv_file_path: str, json_file_path: str):
    """
    Save emails to CSV and JSON files.

    Args:
        emails (List[Dict]): List of email data to save.
        csv_file_path (str): File path for CSV output.
        json_file_path (str): File path for JSON output.
    """
    if emails:
        append_emails_to_csv(emails, csv_file_path)
        append_emails_to_json(emails, json_file_path)


def fetch_emails(
    sender: Optional[str] = None,
    recipient: Optional[str] = None,
    max_results: int = 10,
):
    """
    Fetch emails from Gmail based on sender and recipient filters.

    Args:
        sender (Optional[str]): Sender email address to filter.
        recipient (Optional[str]): Recipient email address to filter.
        max_results (int): Maximum number of emails to fetch.
    """
    creds = authenticate()
    service = build("gmail", "v1", credentials=creds)

    # Prepare file paths
    base_name = "emails"
    csv_file_path = os.path.join(
        DATA_DIR_RAW, generate_filename(base_name, sender, recipient, file_type="csv")
    )
    json_file_path = os.path.join(
        DATA_DIR_RAW, generate_filename(base_name, sender, recipient, file_type="json")
    )
    processed_emails_file = os.path.join(
        DATA_DIR_PROCESSED,
        generate_filename(
            base_name, sender, recipient, suffix="processed", file_type="txt"
        ),
    )
    processed_emails = load_processed_emails(processed_emails_file)

    # Build query string and fetch emails
    query_string = build_query(sender, recipient)
    total_fetched = 0
    next_page_token = None

    with tqdm(total=max_results, desc="Fetching Emails", unit="email") as pbar:
        while total_fetched < max_results:
            messages, next_page_token = fetch_message_ids(
                service, query_string, max_results - total_fetched, next_page_token
            )
            if not messages:
                logger.info(f"No more emails found matching query: {query_string}")
                break
            emails = process_messages(
                service, messages, processed_emails, processed_emails_file
            )
            save_emails(emails, csv_file_path, json_file_path)
            fetched = len(emails)
            total_fetched += fetched
            logger.info(
                f"Fetched {fetched} emails this batch. Total fetched: {total_fetched}"
            )
            pbar.update(fetched)
            if not next_page_token:
                break
    logger.success(f"Finished fetching {total_fetched} emails.")
