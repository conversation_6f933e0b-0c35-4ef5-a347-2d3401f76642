:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"
SET "__venv_name__=venv"


:: =============================================================================
:: venv: locate
:: =============================================================================
SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
:LocateVenv
    IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
    SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
        ECHO Not found: %__venv_identifier__%
        ECHO make sure you've initialized the venv.
        CD /D "%__init_path__%"
        PAUSE>NUL & EXIT /B
    )
GOTO LocateVenv


:: =============================================================================
:: venv: activate
:: =============================================================================
:ActivateVenv
    SET "__venv_stem__=%CD%"
    CD /D "%__init_path__%"
    CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
    ::
    ECHO __init_path__: %__init_path__%
    ECHO __venv_stem__: %__venv_stem__%
    ECHO.
    GOTO ExecuteCommand


:: =============================================================================
:: cmd: terminal
:: =============================================================================
:ExecuteCommand
    SET "__venv_activate__=%__venv_stem__%\%__venv_name__%\Scripts\activate"
    CMD /K ""%__venv_activate__%" & TITLE ^(venv^) %__base_name__%"


:: =============================================================================
:: cmd: exit
:: =============================================================================
ECHO. & ECHO Window will close in 10 seconds ...& PING 127.0.0.1 -n 10 > NUL
EXIT /B
