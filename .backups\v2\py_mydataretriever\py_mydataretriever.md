# Project Files Documentation for `py_mydataretriever`

*Files marked with `[-]` are included in the filestructure preview but are not included in the content generation.

### File Structure

```
[-] ├── .gitignore
[ ] ├── __init__.py
[ ] └── configs
[ ] │   ├── __init__.py
[-] │   ├── credentials.json
[ ] │   ├── settings.py
[-] │   ├── token.json
[ ] └── data
[ ] │   ├── __init__.py
[ ] │   └── processed
[ ] │   │   └── emails
[-] │   │   │   ├── emails_from_jh_dot_paulsen_at_gmail_dot_com_to_jh_dot_paulsen_at_gmail_dot_com_processed.txt
[-] │   │   │   ├── emails_from_kimtuvsjoen_at_gmail_dot_com_to_jh_dot_paulsen_at_gmail_dot_com_processed.txt
[-] │   │   │   ├── emails_from_y_dot_engbraten_at_gmail_dot_com_to_jh_dot_paulsen_at_gmail_dot_com_processed.txt
[ ] │   └── raw
[ ] │   │   └── emails
[-] │   │   │   ├── emails_from_jh_dot_paulsen_at_gmail_dot_com_to_jh_dot_paulsen_at_gmail_dot_com.csv
[-] │   │   │   ├── emails_from_jh_dot_paulsen_at_gmail_dot_com_to_jh_dot_paulsen_at_gmail_dot_com.json
[-] │   │   │   ├── emails_from_kimtuvsjoen_at_gmail_dot_com_to_jh_dot_paulsen_at_gmail_dot_com.csv
[-] │   │   │   ├── emails_from_kimtuvsjoen_at_gmail_dot_com_to_jh_dot_paulsen_at_gmail_dot_com.json
[-] │   │   │   ├── emails_from_y_dot_engbraten_at_gmail_dot_com_to_jh_dot_paulsen_at_gmail_dot_com.csv
[-] │   │   │   ├── emails_from_y_dot_engbraten_at_gmail_dot_com_to_jh_dot_paulsen_at_gmail_dot_com.json
[ ] └── fetchers
[ ] │   ├── __init__.py
[ ] │   ├── gmail_fetcher.py
[ ] ├── main.py
[ ] └── processors
[ ] │   ├── __init__.py
[ ] └── scripts
[ ] │   ├── fetch_emails.py
[ ] └── tests
[ ] │   ├── __init__.py
[ ] │   ├── test_fetchers.py
[ ] │   ├── test_processors.py
[ ] │   ├── test_utils.py
[ ] └── utils
[ ] │   ├── __init__.py
[ ] │   ├── file_utils.py
[ ] │   ├── logging_config.py
```
### 1. `__init__.py`

#### `__init__.py`

```python
# py_mydataretriever/__init__.py

"""
Package: py_mydataretriever
===========================

A package for fetching and retrieving personal online data, such as emails,
YouTube logs/playlists, etc.

"""

```
### 2. `configs\__init__.py`

#### `configs\__init__.py`

```python
# py_mydataretriever/configs/__init__.py

"""
Config Package Initialization
=============================

Initializes the configs package.

"""

```
### 3. `configs\settings.py`

#### `configs\settings.py`

```python
# py_mydataretriever/configs/settings.py

"""
Module: settings
================

Contains configuration variables and constants used throughout the project.

"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
ENV_PATH = os.path.join(os.path.dirname(__file__), '..', '..', '.env')
load_dotenv(dotenv_path=ENV_PATH)

# Google API Configuration
CLIENT_SECRET_FILE = os.getenv(
    "GOOGLE_CLIENT_SECRET_FILE",
    os.path.join(os.path.dirname(__file__), 'credentials.json')
)
TOKEN_FILE = os.getenv(
    "GOOGLE_TOKEN_FILE",
    os.path.join(os.path.dirname(__file__), 'token.json')
)
SCOPES = os.getenv(
    "GOOGLE_SCOPES",
    "https://www.googleapis.com/auth/gmail.readonly"
).split()

# Data Directories
DATA_DIR_RAW = os.path.join(os.path.dirname(__file__), '..', 'data', 'raw', 'emails')
DATA_DIR_PROCESSED = os.path.join(os.path.dirname(__file__), '..', 'data', 'processed', 'emails')

# Ensure data directories exist
os.makedirs(DATA_DIR_RAW, exist_ok=True)
os.makedirs(DATA_DIR_PROCESSED, exist_ok=True)

```
### 4. `data\__init__.py`

#### `data\__init__.py`

```python
# py_mydataretriever/data/__init__.py

"""
Data Package Initialization
===========================

Initializes the data package.

"""

```
### 5. `fetchers\__init__.py`

#### `fetchers\__init__.py`

```python
# py_mydataretriever/fetchers/__init__.py

"""
Fetchers Package Initialization
===============================

Initializes the fetchers package.

"""

```
### 6. `fetchers\gmail_fetcher.py`

#### `fetchers\gmail_fetcher.py`

```python
# py_mydataretriever/fetchers/gmail_fetcher.py

"""
Module: gmail_fetcher
=====================

Fetch emails from a Gmail account using the Gmail API. Supports filtering by
sender or recipient, saving results to CSV and JSON files, and tracking
processed emails to avoid duplicates.

"""

import os
import re
from typing import List, Dict, Optional, Set

from tqdm import tqdm
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

from py_mydataretriever.utils.logging_config import logger
from py_mydataretriever.utils.file_utils import (
    generate_filename,
    append_emails_to_csv,
    append_emails_to_json,
    load_processed_emails,
    save_processed_email,
    safe_strip,
)
from py_mydataretriever.configs.settings import (
    CLIENT_SECRET_FILE,
    TOKEN_FILE,
    SCOPES,
    DATA_DIR_RAW,
    DATA_DIR_PROCESSED,
)


def authenticate() -> Credentials:
    """
    Authenticate and retrieve credentials for Gmail API.

    Returns:
        Credentials: Authenticated credentials object.
    """
    creds: Optional[Credentials] = None
    if os.path.exists(TOKEN_FILE):
        creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
        logger.info("Loaded existing credentials from token file.")
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
            logger.info("Credentials refreshed.")
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_FILE, SCOPES)
            creds = flow.run_local_server(port=0)
            logger.info("New credentials obtained.")
        with open(TOKEN_FILE, "w") as token:
            token.write(creds.to_json())
            logger.info("Credentials saved to token file.")
    return creds


def build_query(sender: Optional[str], recipient: Optional[str]) -> str:
    """
    Construct a Gmail search query string.

    Args:
        sender (Optional[str]): Sender email address.
        recipient (Optional[str]): Recipient email address.

    Returns:
        str: Gmail API query string.
    """
    query = []
    if sender:
        query.append(f"from:{sender}")
    if recipient:
        query.append(f"to:{recipient}")
    return " ".join(query)


def fetch_message_ids(
    service, query_string: str, max_results: int, next_page_token: Optional[str]
) -> (List[Dict], Optional[str]):
    """
    Fetch message IDs matching the query string.

    Args:
        service: Gmail API service instance.
        query_string (str): Gmail API query string.
        max_results (int): Maximum number of results to fetch.
        next_page_token (Optional[str]): Token for pagination.

    Returns:
        Tuple[List[Dict], Optional[str]]: List of message IDs and next page token.
    """
    result = (
        service.users()
        .messages()
        .list(
            userId="me",
            q=query_string,
            maxResults=min(100, max_results),
            pageToken=next_page_token,
        )
        .execute()
    )
    return result.get("messages", []), result.get("nextPageToken")


def get_header_value(headers: List[Dict], name: str) -> Optional[str]:
    """
    Retrieve the value of a specific header by its name.

    Args:
        headers (List[Dict]): List of email headers.
        name (str): Header name to search for.

    Returns:
        Optional[str]: Header value if found, else None.
    """
    for header in headers:
        if header["name"].lower() == name.lower():
            return header["value"]
    return None


def clean_email_address(email_address: str) -> str:
    """
    Standardize the format of an email address.

    Args:
        email_address (str): Raw email address string.

    Returns:
        str: Cleaned email address.
    """
    match = re.match(r'(?:"?([^"]*)"?\s)?(?:<?(.+@[^>]+)>?)', email_address)
    if match:
        name, email = match.groups()
        if name:
            return f"{name.strip()} <{email.strip()}>"
        else:
            return email.strip()
    return email_address.strip()


def process_messages(
    service,
    messages: List[Dict],
    processed_emails: Set[str],
    processed_emails_file: str,
) -> List[Dict]:
    """
    Retrieve and process messages by fetching details like subject and sender.

    Args:
        service: Gmail API service instance.
        messages (List[Dict]): List of message metadata.
        processed_emails (Set[str]): Set of already processed email IDs.
        processed_emails_file (str): File path to save processed email IDs.

    Returns:
        List[Dict]: List of processed email data.
    """
    emails = []
    for message in messages:
        email_id = message["id"]
        if email_id in processed_emails:
            continue
        msg = service.users().messages().get(userId="me", id=email_id).execute()
        headers = msg["payload"]["headers"]
        email_data = {
            "date": safe_strip(get_header_value(headers, "Date")),
            "from": safe_strip(clean_email_address(get_header_value(headers, "From"))),
            "to": safe_strip(
                clean_email_address(get_header_value(headers, "To"))
                if get_header_value(headers, "To")
                else ""
            ),
            "subject": safe_strip(get_header_value(headers, "Subject")),
            "snippet": safe_strip(msg.get("snippet", "")),
        }
        emails.append(email_data)
        save_processed_email(email_id, processed_emails_file)
    return emails


def save_emails(emails: List[Dict], csv_file_path: str, json_file_path: str):
    """
    Save emails to CSV and JSON files.

    Args:
        emails (List[Dict]): List of email data to save.
        csv_file_path (str): File path for CSV output.
        json_file_path (str): File path for JSON output.
    """
    if emails:
        append_emails_to_csv(emails, csv_file_path)
        append_emails_to_json(emails, json_file_path)


def fetch_emails(
    sender: Optional[str] = None,
    recipient: Optional[str] = None,
    max_results: int = 10,
):
    """
    Fetch emails from Gmail based on sender and recipient filters.

    Args:
        sender (Optional[str]): Sender email address to filter.
        recipient (Optional[str]): Recipient email address to filter.
        max_results (int): Maximum number of emails to fetch.
    """
    creds = authenticate()
    service = build("gmail", "v1", credentials=creds)

    # Prepare file paths
    base_name = "emails"
    csv_file_path = os.path.join(
        DATA_DIR_RAW, generate_filename(base_name, sender, recipient, file_type="csv")
    )
    json_file_path = os.path.join(
        DATA_DIR_RAW, generate_filename(base_name, sender, recipient, file_type="json")
    )
    processed_emails_file = os.path.join(
        DATA_DIR_PROCESSED,
        generate_filename(
            base_name, sender, recipient, suffix="processed", file_type="txt"
        ),
    )
    processed_emails = load_processed_emails(processed_emails_file)

    # Build query string and fetch emails
    query_string = build_query(sender, recipient)
    total_fetched = 0
    next_page_token = None

    with tqdm(total=max_results, desc="Fetching Emails", unit="email") as pbar:
        while total_fetched < max_results:
            messages, next_page_token = fetch_message_ids(
                service, query_string, max_results - total_fetched, next_page_token
            )
            if not messages:
                logger.info(f"No more emails found matching query: {query_string}")
                break
            emails = process_messages(
                service, messages, processed_emails, processed_emails_file
            )
            save_emails(emails, csv_file_path, json_file_path)
            fetched = len(emails)
            total_fetched += fetched
            logger.info(
                f"Fetched {fetched} emails this batch. Total fetched: {total_fetched}"
            )
            pbar.update(fetched)
            if not next_page_token:
                break
    logger.success(f"Finished fetching {total_fetched} emails.")

```
### 7. `main.py`

#### `main.py`

```python
# py_mydataretriever/main.py

"""
Main Module
===========

Entry point for the py_mydataretriever package. Orchestrates the data retrieval
process.

"""

# def main():
#     print("Welcome to py_mydataretriever!")
#     # Add code to initiate processes as needed.

# if __name__ == "__main__":
#     main()
# scripts/fetch_emails.py

"""
Script: fetch_emails
====================

Initiates the email fetching process using Gmail Fetcher.

"""

import sys
import os

# Adjust the system path to import the package
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from py_mydataretriever.fetchers.gmail_fetcher import fetch_emails

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Fetch emails from Gmail.")
    parser.add_argument("--sender", type=str, help="Specify the sender email address")
    parser.add_argument("--recipient", type=str, help="Specify the recipient email address")
    parser.add_argument(
        "--max_results", type=int, default=10, help="Maximum number of emails to fetch"
    )
    args = parser.parse_args()
    fetch_emails(sender=args.sender, recipient=args.recipient, max_results=args.max_results)

```
### 8. `processors\__init__.py`

#### `processors\__init__.py`

```python
# py_mydataretriever/processors/__init__.py

"""
Processors Package Initialization
=================================

Initializes the processors package.

"""

```
### 9. `scripts\fetch_emails.py`

#### `scripts\fetch_emails.py`

```python
# scripts/fetch_emails.py

"""
Script: fetch_emails
====================

Initiates the email fetching process using Gmail Fetcher.

"""

import sys
import os

# Adjust the system path to import the package
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from py_mydataretriever.fetchers.gmail_fetcher import fetch_emails

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Fetch emails from Gmail.")
    parser.add_argument("--sender", type=str, help="Specify the sender email address")
    parser.add_argument("--recipient", type=str, help="Specify the recipient email address")
    parser.add_argument(
        "--max_results", type=int, default=10, help="Maximum number of emails to fetch"
    )
    args = parser.parse_args()
    fetch_emails(sender=args.sender, recipient=args.recipient, max_results=args.max_results)

```
### 10. `tests\__init__.py`

#### `tests\__init__.py`

```python
# py_mydataretriever/tests/__init__.py

"""
Tests Package Initialization
============================

Initializes the tests package.

"""

```
### 11. `tests\test_fetchers.py`

#### `tests\test_fetchers.py`

```python
# py_mydataretriever/tests/test_fetchers.py

"""
Module: test_fetchers
=====================

Unit tests for the fetchers package.

"""

import unittest
from py_mydataretriever.fetchers.gmail_fetcher import build_query

class TestGmailFetcher(unittest.TestCase):
    def test_build_query(self):
        self.assertEqual(build_query("<EMAIL>", None), "from:<EMAIL>")
        self.assertEqual(build_query(None, "<EMAIL>"), "to:<EMAIL>")
        self.assertEqual(
            build_query("<EMAIL>", "<EMAIL>"),
            "from:<EMAIL> to:<EMAIL>"
        )

if __name__ == "__main__":
    unittest.main()

```
### 12. `tests\test_processors.py`

#### `tests\test_processors.py`

```python
# py_mydataretriever/tests/test_processors.py

"""
Module: test_processors
=======================

Unit tests for the processors package.

"""

import unittest

class TestProcessors(unittest.TestCase):
    def test_example(self):
        # Placeholder for actual tests
        self.assertTrue(True)

if __name__ == "__main__":
    unittest.main()

```
### 13. `tests\test_utils.py`

#### `tests\test_utils.py`

```python
# py_mydataretriever/tests/test_utils.py

"""
Module: test_utils
==================

Unit tests for the utils package.

"""

import unittest
from py_mydataretriever.utils.file_utils import sanitize_email

class TestFileUtils(unittest.TestCase):
    def test_sanitize_email(self):
        email = "<EMAIL>"
        expected = "user_at_example_dot_com"
        self.assertEqual(sanitize_email(email), expected)

if __name__ == "__main__":
    unittest.main()

```
### 14. `utils\__init__.py`

#### `utils\__init__.py`

```python
# py_mydataretriever/utils/__init__.py

"""
Utils Package Initialization
============================

Initializes the utils package.

"""

```
### 15. `utils\file_utils.py`

#### `utils\file_utils.py`

```python
# py_mydataretriever/utils/file_utils.py

"""
Module: file_utils
==================

Utility functions for file operations such as generating filenames,
appending data to files, and loading processed items.

"""

import os
import csv
import json
from typing import List, Dict, Set, Optional

def generate_filename(
    base_name: str,
    sender: Optional[str] = None,
    recipient: Optional[str] = None,
    suffix: str = "",
    file_type: str = "csv",
) -> str:
    """
    Generate a consistent filename based on sender, recipient, and suffix.

    Args:
        base_name (str): Base name for the file.
        sender (Optional[str]): Sender email address.
        recipient (Optional[str]): Recipient email address.
        suffix (str): Additional suffix for the filename.
        file_type (str): File extension/type.

    Returns:
        str: Generated filename.
    """
    parts = [base_name]
    if sender:
        parts.append(f"from_{sanitize_email(sender)}")
    if recipient:
        parts.append(f"to_{sanitize_email(recipient)}")
    if suffix:
        parts.append(suffix)
    filename = f"{'_'.join(parts)}.{file_type}"
    return filename

def sanitize_email(email: str) -> str:
    """
    Sanitize an email address for use in filenames.

    Args:
        email (str): Email address to sanitize.

    Returns:
        str: Sanitized email address.
    """
    return email.replace("@", "_at_").replace(".", "_dot_")

def append_emails_to_csv(emails: List[Dict], file_path: str):
    """
    Append a list of emails to a CSV file.

    Args:
        emails (List[Dict]): List of email data dictionaries.
        file_path (str): File path to the CSV file.
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    file_exists = os.path.isfile(file_path)
    with open(file_path, mode="a", newline="", encoding="utf-8-sig") as file:
        writer = csv.writer(file)
        if not file_exists:
            # Write header if file doesn't exist
            writer.writerow(["date", "from", "to", "subject", "snippet"])
        for email in emails:
            writer.writerow(
                [
                    email["date"],
                    email["from"],
                    email["to"],
                    email["subject"],
                    email["snippet"],
                ]
            )

def append_emails_to_json(emails: List[Dict], file_path: str):
    """
    Append a list of emails to a JSON file.

    Args:
        emails (List[Dict]): List of email data dictionaries.
        file_path (str): File path to the JSON file.
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    if not os.path.exists(file_path):
        with open(file_path, "w", encoding="utf-8") as file:
            json.dump([], file)
    with open(file_path, "r+", encoding="utf-8") as file:
        existing_data = json.load(file)
        existing_data.extend(emails)
        file.seek(0)
        json.dump(existing_data, file, ensure_ascii=False, indent=4)

def load_processed_emails(file_path: str) -> Set[str]:
    """
    Load the list of already processed email IDs.

    Args:
        file_path (str): File path to the processed emails file.

    Returns:
        Set[str]: Set of processed email IDs.
    """
    if os.path.exists(file_path):
        with open(file_path, "r") as f:
            return set(f.read().splitlines())
    return set()

def save_processed_email(email_id: str, file_path: str):
    """
    Append an email ID to the processed emails file.

    Args:
        email_id (str): Email ID to save.
        file_path (str): File path to the processed emails file.
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "a") as f:
        f.write(email_id + "\n")

def safe_strip(value: Optional[str]) -> str:
    """
    Safely strip a value; returns an empty string if None.

    Args:
        value (Optional[str]): String value to strip.

    Returns:
        str: Stripped string or empty string if None.
    """
    return value.strip() if value else ""

```
### 16. `utils\logging_config.py`

#### `utils\logging_config.py`

```python
# py_mydataretriever/utils/logging_config.py

"""
Module: logging_config
======================

Configures logging behavior for the project using loguru.

"""

from loguru import logger
import sys
import os

# Configure loguru logger
logger.remove()  # Remove default handler
logger.add(
    sys.stderr,
    level="INFO",
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
           "<level>{level: <8}</level> | "
           "<cyan>{module}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
           "<level>{message}</level>",
)

# Optional: Add file handler
LOG_DIR = os.path.join(os.path.dirname(__file__), '..', 'logs')
os.makedirs(LOG_DIR, exist_ok=True)
logger.add(
    os.path.join(LOG_DIR, "app.log"),
    rotation="10 MB",
    retention="10 days",
    level="INFO",
    compression="zip",
    serialize=False,
    backtrace=True,
    diagnose=True,
)

```
