# ChatGPT Conversation Fetcher
# =======================================================
# Fetch conversation history from OpenAI's ChatGPT.
# Organize conversations into JSON files, track processed
# conversations to avoid duplicates, and enable CLI customization.
# =======================================================

# Imports
# -------------------------------------------------------
from typing import Dict, Any, List
from tqdm import tqdm
from loguru import logger
from pathlib import Path
import re
import json
import argparse
import os
import sys
from datetime import datetime, timezone
from dotenv import load_dotenv
from openai import OpenAI

# Load Environment Variables
# -------------------------------------------------------
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY environment variable is required")
    sys.exit(1)

# Define Directories
# -------------------------------------------------------
SCRIPT_DIR = Path(__file__).parent.resolve()
DATA_DIR = SCRIPT_DIR / "data"
LOGS_DIR = SCRIPT_DIR / "logs"
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Configure loguru for structured logging
# -------------------------------------------------------
logger.remove()
log_file = LOGS_DIR / "chatgpt_fetcher.log"
logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)

# Utility Functions
# -------------------------------------------------------
def sanitize_filename_component(component: str) -> str:
    """Replace invalid filename characters with underscores."""
    return re.sub(r'[^A-Za-z0-9_-]', '_', component)

def sanitize_filename(base_name: str, identifier: str = None, suffix: str = "", file_type: str = "json") -> Path:
    """Generate a sanitized filename based on provided components."""
    parts = [base_name]
    if identifier:
        identifier_sanitized = sanitize_filename_component(identifier)
        parts.append(identifier_sanitized)
    if suffix:
        suffix_sanitized = sanitize_filename_component(suffix)
        parts.append(suffix_sanitized)
    filename = f"{'_'.join(parts)}.{file_type}"
    return DATA_DIR / filename

# ChatGptFetcher Class
# -------------------------------------------------------
class ChatGptFetcher:
    """Fetch and process OpenAI ChatGPT conversation history."""

    def __init__(self, output_dir: Path, limit: int = 100):
        # Path(__file__).parent.resolve()
        print(f'output_dir: {output_dir}')
        self.output_dir = output_dir
        self.limit = limit
        client =OpenAI(api_key=OPENAI_API_KEY)
        self.processed_file = self._get_processed_file()
        self.processed_ids = self.load_processed_runs()

    def _get_processed_file(self) -> Path:
        """Generate processed runs tracking file path."""
        return self.output_dir / "processed_runs.txt"

    def load_processed_runs(self) -> set:
        """Load processed run IDs to avoid duplicates."""
        if self.processed_file.exists():
            try:
                with self.processed_file.open("r") as f:
                    return set(line.strip() for line in f)
            except Exception as e:
                logger.error(f"Failed to load processed runs: {e}")
                return set()
        return set()

    def save_processed_run(self, run_id: str):
        """Save a processed run ID."""
        try:
            self.processed_file.parent.mkdir(parents=True, exist_ok=True)
            with self.processed_file.open("a") as f:
                f.write(f"{run_id}\n")
            self.processed_ids.add(run_id)
        except Exception as e:
            logger.error(f"Failed to save processed run {run_id}: {e}")

    def save_conversation(self, conversation: Dict[str, Any]):
        """Save a conversation to a JSON file."""
        created_at = datetime.fromtimestamp(
            conversation.get("created", datetime.now(timezone.utc).timestamp()),
            tz=timezone.utc
        )
        year_month = created_at.strftime("%Y/%m")
        save_dir = self.output_dir / year_month
        save_dir.mkdir(parents=True, exist_ok=True)

        run_id = conversation.get("id", "unknown")
        filename = save_dir / f"conversation_{run_id}.json"

        try:
            with filename.open("w", encoding="utf-8") as f:
                json.dump(conversation, f, ensure_ascii=False, indent=4)
            logger.info(f"Saved conversation {run_id} to {filename}")
        except Exception as e:
            logger.error(f"Failed to save conversation {run_id}: {e}")

    def fetch_history(self):
        """Fetch and save available chat history."""
        total_fetched = 0

        try:
            conversations = self.retrieve_conversations()
            with tqdm(total=min(self.limit, len(conversations)), desc="Fetching Chat History") as pbar:
                for conversation in conversations:
                    run_id = conversation.get("id")
                    if run_id in self.processed_ids:
                        continue

                    self.save_conversation(conversation)
                    self.save_processed_run(run_id)
                    total_fetched += 1
                    pbar.update(1)

                    if total_fetched >= self.limit:
                        break

            logger.success(f"Finished fetching {total_fetched} new conversations.")

        except Exception as e:
            logger.error(f"Error fetching chat history: {e}")
            raise

    def retrieve_conversations(self) -> List[Dict[str, Any]]:
        """Retrieve conversation history from OpenAI API."""
        conversations = []
        try:
            # Example logic for a paginated API call (if such an endpoint exists)
            next_cursor = None
            while True:
                # Simulate API request parameters
                request_params = {
                    "limit": 20,  # Fetch 20 conversations per request
                    "cursor": next_cursor,  # Handle pagination
                }
                response = client.chat.completions.create(**request_params)  # Replace with actual API call if available

                # Check response structure
                if "data" not in response or not isinstance(response["data"], list):
                    logger.error("Unexpected response structure from OpenAI API.")
                    break

                # Extract conversations and append to the list
                for conv in response["data"]:
                    conversations.append({
                        "id": conv.get("id", "unknown"),
                        "created": conv.get("created", datetime.now(timezone.utc).timestamp()),
                        "messages": conv.get("messages", [])
                    })

                # Check for next page
                next_cursor = response.get("next_cursor")
                if not next_cursor or len(conversations) >= self.limit:
                    break

            logger.info(f"Retrieved {len(conversations)} conversations from OpenAI API.")
            return conversations

        except Exception as e:
            logger.error(f"Unexpected error while retrieving conversations: {e}")
            return []


# Command-Line Interface Enhancements
# -------------------------------------------------------
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Fetch and organize OpenAI ChatGPT conversation history.",
        epilog="Example: python chatgpt_fetcher.py --limit 500"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=100,
        help="Maximum number of conversations to fetch (default: 100)."
    )
    parser.add_argument(
        "--data_dir",
        type=str,
        default="data",
        help="Directory to save chat history (default: data)."
    )
    parser.add_argument(
        "--logs_dir",
        type=str,
        default="logs",
        help="Specify the directory to save log files (default: logs)."
    )
    return parser.parse_args()

# Main Execution Flow
# -------------------------------------------------------
def main():
    """Execute the chat history fetching process based on CLI arguments."""
    args = parse_arguments()

    # Update directories based on arguments or use defaults
    SCRIPT_DIR = Path(__file__).parent.resolve()
    data_dir = SCRIPT_DIR / args.data_dir if args.data_dir else DATA_DIR
    logs_dir = SCRIPT_DIR / args.logs_dir if args.logs_dir else LOGS_DIR
    data_dir.mkdir(parents=True, exist_ok=True)
    logs_dir.mkdir(parents=True, exist_ok=True)

    # Reconfigure logger to use the specified logs directory
    global logger
    logger.remove()
    log_file = logs_dir / "chatgpt_fetcher.log"
    logger.add(log_file, rotation="10 MB", retention="10 days", level="INFO", serialize=True)
    fetcher = ChatGptFetcher(
        output_dir=data_dir,
        limit=args.limit
    )
    fetcher.fetch_history()

if __name__ == "__main__":
    main()

